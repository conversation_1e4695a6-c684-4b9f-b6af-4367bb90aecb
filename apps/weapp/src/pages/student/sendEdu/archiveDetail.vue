<script setup lang="ts">
import {ref} from 'vue';
import {onLoad, onPullDownRefresh, onShow} from '@dcloudio/uni-app';
import {request} from '@repo/infrastructure/request';
import {PROJECT_URLS} from '@/common/env';
import {goPage, goWebview} from '@/common/page';
import WebView from "@/pages/common/webView.vue";

const d2d = ref<any>({});
const records = ref<any[]>([]);
const d2dId = ref(0);
const isParent = ref(false);

const teacherMode = ref(false);

const loadData = async () => {
  if (d2dId.value <= 0) {
    return;
  }
  uni.showLoading({title: '加载中', mask: true});

  try {
    const {data} = (await request(`/resourceRoom/d2dEducation/${d2dId.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    })) as any;

    const {data: recordsList} = await request('/resourceRoom/d2dEducationRecord', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        d2dEducation: data.id,
        pageSize: 999,
      },
    });

    d2d.value = data as any;
    records.value = (recordsList?.items as any[]) || [];
  } finally {
    uni.hideLoading();
  }
};

const handleGoCustomDetail = () => {
  goWebview(`/sendEducationPlan/detail?id=${d2dId.value}`);
};

const handleGoArchiveEdit = (record?: any) => {
  goWebview(`/sendEducationPlan/archiveEdit?d2dId=${d2dId.value}&id=${record?.id || ''}&isParent=${isParent.value}`);
};

const handleGoD2dRecordEdit = (record?: any) => {
  goWebview(`/sendEducationPlan/d2dRecordEdit?d2dId=${d2dId.value}&id=${record?.id || ''}&isParent=${isParent.value}`);
};
const handleCreateD2dRecordEdit = () => {
  goWebview(`/sendEducationPlan/d2dRecordEdit?d2dId=${d2dId.value}&isParent=${isParent.value}`);
};

const activeBar = ref('archive')

onLoad(async (query: any) => {
  d2dId.value = query.id;
  teacherMode.value = query.teacherMode;
  isParent.value = query?.isParent==='true';
});

onShow(async () => {
  await loadData();
});

onPullDownRefresh(async () => {
  await loadData();
  uni.stopPullDownRefresh();
});
</script>

<template>
  <view v-if="d2d.id" class="p-4">
    <view>
      <div class="w-full flex justify-center items-center space-x-2 mb-4">
        <div
            class="w-1/2 text-center h-full bg-gray-100 border rounded text-sm top-nav"
            :class="activeBar==='archive'?'active font-bold shadow':''"
            @click="activeBar='archive'">
          送教档案
        </div>
        <div
            class="w-1/2 text-center h-full bg-gray-100 border rounded text-sm top-nav"
            :class="activeBar==='record'?'active font-bold shadow':''"
            @click="activeBar='record'">
          送教记录
        </div>
      </div>
    </view>
    <!--送教档案-->
    <view v-if="activeBar==='archive'">
      <view class="rounded-lg overflow-hidden ">
        <uni-section title="基本信息" type="line">
          <uni-list :border="false">
            <uni-list-item :title="d2d.dateRange?.join(' ~ ')"/>
            <uni-list-item title="状态" :right-text="d2d.finished ? '已结束' : '进行中'"/>
            <uni-list-item title="学生姓名" :right-text="`${d2d.student?.gradeClass?.name} ${d2d.student?.name}`"/>
            <uni-list-item title="所属学校" :right-text="d2d.student?.fusionSchoolName"/>
            <uni-list-item title="学期" :right-text="d2d.period"/>
            <uni-list-item title="送教负责人" :right-text="d2d.personInCharge"/>
            <uni-list-item title="共享人" :note="d2d?.collaborators.map(c=>c?.name).join(', ')"/>
            <uni-list-item
                v-if="false"
                title="查看详情"
                clickable
                show-arrow
                @click="handleGoCustomDetail"
            />
          </uni-list>
        </uni-section>
      </view>

      <view class="rounded-lg overflow-hidden mt-4">
        <uni-section title="档案详情">
          <uni-list v-if="d2d && teacherMode" :border="false">
            <uni-list-item v-for="(item,dex) in d2d?.docTemplates " :key="item.id" clickable show-arrow @click="handleGoArchiveEdit(item)">
              <template #header>
                <view class="info">
                  <uni-icons v-if="item?.attachment?.numUdf1" color="green" type="checkmarkempty" size="12"></uni-icons>
                  <uni-icons v-else color="red" type="circle" size="12"></uni-icons>
                  <text class="text-xs ml-1">
                    {{ dex + 1 + '、' }}{{ item?.name }}
                  </text>
                </view>
              </template>
            </uni-list-item>
          </uni-list>
        </uni-section>
      </view>
    </view>
    <view v-else>
        <uni-list v-if="records" :border="false">
          <uni-list-item v-for="(item,dex) in records " :key="item.id" clickable show-arrow @click="handleGoD2dRecordEdit(item)">
            <template #header>
              <view class="info">
                <div>
                  <div class="mb-1">
                    <text class="text-sm mr-2">
                      {{ item?.date }}
                    </text>
                    <uni-tag :inverted="true" :text="item?.type" type="primary" size="mini" class="mr-2"/>
                    <uni-tag v-if="!item?.attachments?.length" :inverted="true" text="无照片" type="error" class="mr-2" size="mini" />
                  </div>
                  <div>
                    <div class="text-green-500 text-sm">
                      {{item?.sendTeachers.map(t=>t?.name).join(', ')}}：{{item?.description || '~'}}
                    </div>
                  </div>
                </div>

              </view>
            </template>
          </uni-list-item>
        </uni-list>
    </view>

    <view v-if="teacherMode&&!isParent">
      <fixed-button-safe/>
      <fixed-bottom-button @click="handleCreateD2dRecordEdit"> 创建送教记录</fixed-bottom-button>
    </view>
  </view>
</template>

<route lang="json">
{
"style": {
"navigationBarTitleText": "送教档案",
"enablePullDownRefresh": true
}
}
</route>

<style scoped lang="scss">
.top-nav {
  padding: 10px 0 10px 0;
}

.active {
  border-color: #48b2ff;
  background-color: #73c5ff;
  color: white;
}
</style>
