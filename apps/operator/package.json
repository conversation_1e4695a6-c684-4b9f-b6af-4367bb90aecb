{"name": "linch-se-operator", "description": "Arco Design Pro for Vue", "version": "2.1.50", "private": true, "author": "ArcoDesign Team", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts --mode=dev", "build": "vite build --config ./config/vite.config.prod.ts", "build:deploy": "vite build --config ./config/vite.config.prod.ts --mode=deploy", "build:test": "vite build --config ./config/vite.config.test.ts --mode=test", "build:whdev": "vite build --config ./config/vite.config.test.ts --mode=whdev", "build:whprod": "vite build --config ./config/vite.config.prod.ts --mode=whprod", "build:v2cloud": "vite build --config ./config/vite.config.test.ts --mode=v2cloud", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@maxbilbow/ant-path-matcher": "^0.0.2", "@repo/components": "workspace:*", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/printer": "workspace:*", "@repo/rich-editor": "workspace:*", "@repo/ui": "workspace:*", "@types/ali-oss": "^6.16.11", "@vueuse/core": "^10.7.2", "arco-design-pro-vue": "^2.7.2", "bin-wrapper": "npm:bin-wrapper-china", "crypto-js": "^4.2.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "query-string": "^8.2.0", "vue": "^3.4.19", "vue-i18n": "^9.9.1", "vue-router": "^4.2.5"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@types/crypto-js": "^4.2.2", "@types/mockjs": "^1.0.10", "@types/node": "^20.11.24", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/babel-plugin-jsx": "^1.2.1", "consola": "^3.2.3", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "husky": "^9.0.11", "less": "^4.2.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "pont-engine": "^1.6.1", "prettier": "^3.2.5", "rollup": "^4.12.0", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^16.2.1", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.26.0", "vite": "^6.0.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^5.1.0", "vue-svg-loader": "^0.16.0", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=14.0.0"}, "__npminstall_done": false, "type": "module"}