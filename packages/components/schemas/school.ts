import { defineAsyncComponent, Ref } from 'vue';
import { PROJECT_URLS } from '@repo/env-config';
import { CustomSchema } from '@repo/infrastructure/types';
import { natures } from '@repo/infrastructure/data/schoolTypes';
import { getItemAttachmentsColumns } from '@repo/ui/components/utils/extraContent';

const schoolItemAttachmentsColumns = getItemAttachmentsColumns({
  defaultOptions: ['营业执照'],
});

const schoolSchema: CustomSchema = {
  api: '/resourceCenter/fusionSchool',
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  formViewProps: {
    layout: 'horizontal',
    colSpan: 12,
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: ['parentId', 'name', 'nature', 'hasResourceRoom'],
        colSpan: 12,
      },
      {
        label: '证件资料',
        fields: ['itemAttachments'],
      },
    ],
  },
  detailViewProps: {
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: ['name', 'nature', 'hasResourceRoom'],
      },
      {
        label: '证件资料',
        fields: ['itemAttachments'],
      },
    ],
  },
  fieldsMap: {
    itemAttachments: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: schoolItemAttachmentsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: schoolItemAttachmentsColumns,
      },
    },
    nature: {
      key: 'nature',
      inputWidget: 'selectInput',
      inputWidgetProps: {
        popupContainer: '#org-drop-down-parent-id',
        options: natures,
        onValueChange: (value: any, record: Ref<Record<string, any>>) => {
          record.value.type = '';
          record.value.institutionType = '';
        },
      },
    },
    operationType: {
      key: 'operationType',
      inputWidget: 'selectInput',
      inputWidgetProps: {
        popupContainer: '#org-drop-down-parent-id',
        options: ['公办', '创新'],
      },
    },
    region: {
      key: 'region',
      inputWidget: 'areaSelectInput',
      inputWidgetProps: {
        popupContainer: '#org-drop-down-parent-id',
        allowSearch: true,
      },
    },
    symbol: {
      key: 'symbol',
      visibleInForm: false,
    },
    parentId: {
      label: '上级单位',
      key: 'parentId',
      inputWidget: 'treeSelectInput',
      treeSourceApi: '/org/branchOffice/getTree',
      visibleInDetail: false,
      required: true,
      preload: true,
      inputWidgetProps: {
        popupContainer: '#org-drop-down-parent-id',
        treeSourceApiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
      },
    },
    disabilityNum: {
      key: 'disabilityNum',
      inputWidget: 'numberInput',
    },
  },
};

export default { schoolSchema };
