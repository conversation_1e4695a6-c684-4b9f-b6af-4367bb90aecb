<script setup lang="ts">
  import openapi from '@repo/infrastructure/openapi';
  import { onMounted, ref } from 'vue';
  import { goPage } from '@/common/page';
  import useWebSocket from '@/common/hooks/websocket';
  import useOss from '@/common/hooks/oss';
  import { loginGuard } from '@/common/auth';
  import { PROJECT_URLS } from '@/common/env';
  import { useUserStore } from '@repo/infrastructure/store';
  import { useTeacherStore } from '@/common/store/teacher';
  import { getToken } from '@repo/infrastructure/auth';

  const carousels = ref<API.GuardianWeappCarousel[]>([]);
  const newsList = ref<any[]>([]);
  const announcement = ref<API.GuardianAnnouncement | any>();

  const oss = useOss();
  const currentSwiperIndex = ref(0);
  const userStore = useUserStore();
  const teacherStore = useTeacherStore();

  const userInfo = teacherStore.userInfo;

  const quickButtons = [
    {
      text: '通知',
      icon: 'notification',
      class: 'bg-green-500',
      handler() {
        goPage('/news/announcement');
      },
    },
    {
      text: '教研培训',
      icon: 'compose',
      class: 'bg-cyan-500',
      handler() {
        if (loginGuard('teacher')) {
          goPage('/teacher/conference/index');
        }
      },
    },
    {
      text: '测评系统',
      icon: 'chat',
      class: 'bg-blue-500',
      handler() {
        uni.showToast({
          title: '暂未开放',
          icon: 'none',
        });
      },
    },
    // {
    //   text: '行为分析小诸葛',
    //   icon: 'chat',
    //   class: 'bg-sky-500',
    //   handler() {
    //     if (loginGuard('teacher')) {
    //       goPage('/pages/ai/behaviorAiChat');
    //     }
    //   },
    // },
    {
      text: '巡回指导',
      icon: 'chat',
      class: 'bg-yellow-500',
      permNode: 'dailyWork:advisoryService:tourGuide',
      handler() {
        if (loginGuard('teacher')) {
          goPage('/teacher/tourGuide/index');
        }
      },
    },
  ].filter((item) => {
    if (!item.permNode) {
      return true;
    }

    return userStore.isAuthorized(item.permNode, userInfo.authorities || []);
  }) as any[];

  const init = async () => {
    const { data } = await openapi.guardianController.guardianWeappInit(
      {
        visibleIn: 'teacher',
      },
      { baseURL: PROJECT_URLS.GO_PROJECT_API },
    );
    carousels.value = data.carouselList || [];
    newsList.value = data.newsList || [];
    announcement.value = data.announcementList?.find((item) => item.indexBanner && item.visibleIn?.includes('teacher'));
  };

  const handleSwiperChange = (e: any) => {
    currentSwiperIndex.value = e.detail.current;
  };

  const ws = useWebSocket();

  const handleGoCarousel = (item: API.GuardianWeappCarousel) => {
    console.log(item);
    if (item.link) {
      uni.navigateTo({
        url: item.link,
      });
    }
  };

  onMounted(async () => {
    await init();
    if (getToken()) {
      await ws.checkSocketStatus();
      const { data: unreadCount } = await openapi.genericSessionController.getUnreadMessageCount({
        baseURL: PROJECT_URLS.GO_PROJECT_API,
      });
      if (unreadCount > 0) {
        // uni.showTabBarRedDot({
        //   index: 1,
        // });
      } else {
        // uni.hideTabBarRedDot({
        //   index: 1,
        // });
      }
    }
  });
</script>

<template>
  <view>
    <uni-swiper-dot class="uni-swiper-dot-box" :info="carousels || []" :current="currentSwiperIndex" field="content">
      <swiper class="swiper-box !w-full" :current="currentSwiperIndex" @change="handleSwiperChange">
        <swiper-item v-for="(item, index) in carousels || []" :key="index" class="w-full">
          <view class="swiper-item !w-full" :class="'swiper-item' + index" @click="() => handleGoCarousel(item)">
            <image
              :src="oss.imageThumbUrl(item.image, 750, 400)"
              class="store-photo !w-full"
              mode="aspectFill"
              lazy-load
            />
          </view>
        </swiper-item>
      </swiper>
    </uni-swiper-dot>

    <uni-notice-bar
      v-if="announcement"
      :speed="50"
      :text="`${announcement?.title}`"
      more-text="查看详情"
      :scrollable="true"
      :show-get-more="true"
      :show-icon="true"
      @click="goPage('/news/announcementDetail', { id: announcement?.id })"
    />

    <view class="grid grid-cols-4 px-2 py-4 bg-white text-center">
      <view
        v-for="(btn, index) in quickButtons"
        :key="index"
        class="flex flex-col items-center gap-2 justify-center"
        @tap="btn.handler"
      >
        <view class="rounded-full w-14 h-14 flex items-center justify-center" :class="btn.class">
          <uni-icons :type="btn.icon" size="34" color="#fff" />
        </view>
        <text class="text-sm">{{ btn.text }}</text>
      </view>
    </view>

    <view v-if="newsList.length" class="mt-2 mx-2">
      <uni-section title="最新资讯" type="line">
        <template #right>
          <navigator url="/pages/news/index" class="text-gray-500 text-xs"> 更多 &gt;&gt; </navigator>
        </template>
        <uni-list border>
          <uni-list-item
            v-for="(news, index) in newsList"
            :key="index"
            :title="news.title"
            :ellipsis="1"
            clickable
            show-arrow
            @click="goPage('/news/detail', { id: news.id })"
          />
        </uni-list>
      </uni-section>
    </view>
  </view>
</template>

<style scoped lang="scss">
  .swiper-box,
  .swiper-item,
  .store-photo {
    width: 750rpx;
    height: 400rpx;
  }
</style>
