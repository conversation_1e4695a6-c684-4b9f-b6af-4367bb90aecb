{"name": "@repo/electron-preload", "type": "commonjs", "version": "2.1.50", "private": true, "exports": {".": {"types": "./dist/index.d.ts", "development": "./src/index.ts", "default": "./dist/index.cjs"}, "./main": {"types": "./dist/ipcMain.d.ts", "development": "./src/ipcMain.ts", "default": "./dist/ipcMain.cjs"}, "./dist/*": "./dist/*"}, "types": "./dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=v16.13", "npm": ">=8.1"}, "scripts": {"build": "vite build", "build:deploy": "vite build --mode=deploy", "typecheck": "tsc --noEmit -p tsconfig.json", "postinstall": "if [ \"$CI\" != \"true\" ]; then pnpm run build; else echo 'Skipping Preload postinstall/build in CI environment'; fi"}, "peerDependencies": {"electron": "33.3.1"}, "devDependencies": {"vite-plugin-dts": "^4.0.2"}}