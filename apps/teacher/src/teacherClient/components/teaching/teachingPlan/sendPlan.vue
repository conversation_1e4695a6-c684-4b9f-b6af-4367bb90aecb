<script setup lang="ts">
  import { onMounted, provide, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import customizeComponentViewTemplate from '@repo/ui/components/customizeComponent/customizeComponentViewTemplate.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import useCustomizeComponentStore from '@repo/infrastructure/customizeComponent/store';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { CrudForm } from '@repo/ui/components';
  import { cloneDeep, extend } from 'lodash';
  import { VuePrintNext } from 'vue-print-next';
  import { useUserStore } from '@repo/infrastructure/store';

  import SendEduRecord from './sendEduRecord.vue';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
    notImportedFromExternal: '1',
  };
  const { userInfo } = useUserStore();

  const recordVisible = ref(false);
  const currentPlan = ref(null);
  const editVisible = ref(false);
  const detailsVisible = ref(false);

  const sendRecord = ref();

  const loadSendRecord = async () => {
    const { data: res } = await request('/resourceRoom/sendEducationRecord', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        sendEducationPlan: currentPlan.value.id,
      },
    });
    sendRecord.value = res.items;
  };

  const handleRowAction = async (action, row) => {
    currentPlan.value = row || {};
    if (action.key === 'records') {
      recordVisible.value = true;
    } else if (action.key === 'edit' || action.key === 'add' || action.key === 'editDetails') {
      editVisible.value = true;
    } else if (action.key === 'viewDetails') {
      await loadSendRecord();
      detailsVisible.value = true;
    }
  };

  const formRef = ref(null);
  const tableRef = ref(null);

  const customizeRef = ref(null);
  const sendPlanRecordRef = ref(null);
  const printRef = ref(null);

  const handleSubmit = async () => {
    try {
      const data = await customizeRef.value?.handleSubmit();

      data.student.createdDate = null;
      if (data?.id) {
        await request(`/resourceRoom/sendEducationPlan/${data.id}`, {
          method: 'PUT',
          data,
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
      } else {
        await request('/resourceRoom/sendEducationPlan', {
          method: 'POST',
          data,
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
      }
      setTimeout(async () => {
        await tableRef.value?.loadData();
      }, 50);
    } finally {
      editVisible.value = false;
    }
  };

  provide('SchemaHelper', SchemaHelper);
  provide('request', request);
  provide('PROJECT_URLS', PROJECT_URLS);
  provide('CrudForm', CrudForm);
  provide('cloneDeep', cloneDeep);
  provide('extend', extend);
  provide('RecordDetail', RecordDetail);
  provide('CrudForm', CrudForm);
  provide('VuePrintNext', VuePrintNext);
  provide('customizeComponentViewTemplate', customizeComponentViewTemplate);

  const visibleColumns = ref([
    'period',
    'student',
    'dateRange',
    'planTeacher',
    'finished',
    'personInCharge',
    'collaborators',
    'submitStatus',
  ]);
  const visibleComponents = ref(['quickSearch', 'refresh', 'layout', 'recycleBin']);

  const handleAdd = () => {
    currentPlan.value = {};
    editVisible.value = true;
  };

  const printPageVisible = ref(false);
  const showPrintPage = () => {
    printPageVisible.value = true;
  };

  const handlePrint = async () => {
    await printRef.value?.handlePrint();
  };
  onMounted(async () => {
    const api = '/resourceRoom/sendEducationPlan';
    schema.value = await SchemaHelper.getInstanceByDs(api);
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">
          <slot name="title"> 送教计划</slot>
        </div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          :visible-components="visibleComponents"
          @row-action="handleRowAction"
        >
          <template #supplementary-button>
            <a-button v-if="userInfo?.sendTeacher" size="mini" type="primary" @click="handleAdd">
              <icon-plus />
              新增
            </a-button>
          </template>
        </table-action>
      </div>
    </template>
    <slot name="top-bar"></slot>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      :row-class="getSubmittableRowClass"
      @row-action="handleRowAction"
    >
    </crud-table>
    <a-modal
      :visible="editVisible"
      :render-to-body="false"
      fullscreen
      :on-before-ok="handleSubmit"
      title="送教计划"
      :hide-cancel="customizeRef?.isDefaultComponent"
      :ok-text="customizeRef?.isDefaultComponent ? '返回' : '确定'"
      @cancel="editVisible = false"
    >
      <!--<customized-send-plan-edit-demo v-if="editVisible" ref="customizeRef" v-model:record="currentPlan" />-->
      <customize-component
        v-if="editVisible"
        ref="customizeRef"
        :record="currentPlan"
        :model-value="currentPlan"
        module="SendEducationPlan"
        page="Edit"
      >
        <template #default>
          <crud-form
            v-if="editVisible"
            ref="formRef"
            v-model="currentPlan"
            :schema="schema"
            :show-actions="customizeRef?.isDefaultComponent"
            :callback="() => {}"
          />
        </template>
      </customize-component>
    </a-modal>
  </a-card>
  <a-modal
    v-if="detailsVisible"
    :visible="detailsVisible"
    width="80%"
    :closable="false"
    @cancel="detailsVisible = false"
  >
    <template #title>
      <div class="flex justify-between items-center w-full">
        <span class="flex-grow text-center">查看详情</span>
        <span
          v-if="!sendPlanRecordRef?.isDefaultComponent"
          class="flex-shrink-0 text-gray-500 cursor-pointer"
          @click="showPrintPage"
        >
          <icon-printer />
          打印预览
        </span>
      </div>
    </template>
    <!--<customized-send-plan-view-demo v-if="detailsVisible" :current-plan="currentPlan" :schema="schema" />-->
    <customize-component
      v-if="detailsVisible"
      ref="sendPlanRecordRef"
      v-model="currentPlan"
      :schema="schema"
      :current-plan="currentPlan"
      module="SendEducationPlan"
      page="View"
    >
      <template #default>
        <record-detail v-if="detailsVisible" ref="recordDetailRef" :raw="currentPlan" :schema="schema" />
      </template>
    </customize-component>
  </a-modal>
  <a-modal v-model:visible="printPageVisible" fullscreen title="打印预览">
    <template #title>
      <div class="flex justify-between items-center w-full">
        <span class="flex-grow text-center">打印预览</span>
        <a-button size="mini" class="flex-shrink-0 text-gray-500 cursor-pointer mr-10" @click="handlePrint">
          <icon-printer />
          打印
        </a-button>
      </div>
    </template>
    <!--<customizedSendPlanPrintDemo v-if="printPageVisible" ref="printRef" :plan="currentPlan" :send-record="sendRecord" />-->
    <customize-component
      v-if="printPageVisible"
      ref="printRef"
      v-model="currentPlan"
      :schema="schema"
      :plan="currentPlan"
      :current-plan="currentPlan"
      module="SendEducationPlan"
      page="Print"
      :send-record="sendRecord"
    >
      <template #default>
        <record-detail ref="recordDetailRef" :raw="currentPlan" :schema="schema" />
      </template>
    </customize-component>
  </a-modal>

  <send-edu-record v-if="recordVisible && currentPlan" v-model:visible="recordVisible" :plan="currentPlan" />
</template>
