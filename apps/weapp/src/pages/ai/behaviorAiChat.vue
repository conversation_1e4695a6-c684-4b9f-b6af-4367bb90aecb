<script setup lang="ts">
import {ref, onMounted, nextTick, watch, computed} from 'vue'
import {onLoad, onPullDownRefresh, onShow, onHide} from '@dcloudio/uni-app'
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@/common/env';

// 响应式数据
const isReply = ref(false)
const isLoading = ref(false)
const msg = ref('')
const chatCatch = ref<any[]>([])
const sessionId = ref('')
const chatResponse = ref('')
const scrollViewId = ref('chat-scroll-view')

// 计算属性
const chatContainerHeight = computed(() => {
  // 获取系统信息，计算聊天区域高度
  const systemInfo = uni.getSystemInfoSync()
  const windowHeight = systemInfo.windowHeight
  const statusBarHeight = systemInfo.statusBarHeight || 0
  const navigationBarHeight = 44 // 导航栏高度
  const toolbarHeight = 120 // 底部工具栏高度
  return windowHeight - statusBarHeight - navigationBarHeight - toolbarHeight
})

// 聊天历史管理
const loadChatHistory = () => {
  try {
    const history = uni.getStorageSync(`chat_history_${sessionId.value || 'default'}`)
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.error('加载聊天历史失败:', error)
    return []
  }
}

const saveChatHistory = () => {
  try {
    uni.setStorageSync(`chat_history_${sessionId.value || 'default'}`, JSON.stringify(chatCatch.value))
  } catch (error) {
    console.error('保存聊天历史失败:', error)
  }
}

// 重置状态
const reset = () => {
  isReply.value = false
  msg.value = ''
}

// 滚动到底部
const handleScrollToBottom = async () => {
  await nextTick()
  // 使用 UniApp 的滚动到底部方法
  uni.pageScrollTo({
    scrollTop: 999999,
    duration: 300
  })
}

// 复制消息
const handleCopy = (message: string) => {
  uni.setClipboardData({
    data: message,
    success: () => {
      uni.showToast({
        title: '已复制',
        icon: 'success',
        duration: 2000
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

// 清空聊天历史
const clearChatHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有聊天记录吗？',
    success: (res) => {
      if (res.confirm) {
        chatCatch.value = []
        try {
          uni.removeStorageSync(`chat_history_${sessionId.value || 'default'}`)
          uni.removeStorageSync('behavior_chat_sessionId')
        } catch (error) {
          console.error('清空历史记录失败:', error)
        }
        reset()
        uni.showToast({
          title: '已清空',
          icon: 'success',
          duration: 2000
        })
      }
    }
  })
}

// 发送消息
const handleSend = () => {
  if (!msg.value.trim()) {
    uni.showToast({
      title: '发送消息不能为空',
      icon: 'none',
      duration: 2000
    })
    return
  }

  isReply.value = true
  // 添加用户消息
  chatCatch.value.push({ role: 'user', msg: msg.value })
  // 添加空的机器人消息占位
  chatCatch.value.push({ role: 'robot', msg: '' })

  // 发送API请求
  requestStreamChat(msg.value)

  // 保存历史记录
  saveChatHistory()

  // 滚动到底部
  handleScrollToBottom()
}

// 停止回复
const handleStop = () => {
  isReply.value = false
  isLoading.value = false
  // 这里可以添加取消请求的逻辑
}

// 流式聊天API请求
const requestStreamChat = async (message: string) => {
  try {
    chatResponse.value = ''
    const url = `${PROJECT_URLS.MAIN_PROJECT_API}/resourceRoom/behaviorAnalysis/chat/streamCall`

    const token = uni.getStorageSync('token') || ''

    console.log('token-token-token-token: ',token)

    const params = {
      message: message
    }

    if (sessionId.value) {
      params.sessionId = sessionId.value
    }

    // 使用uni.request进行请求
    uni.request({
      url: url,
      method: 'PUT',
      data: params,
      header: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${token}`,
        'LoginSource': 'WeApp',
        'Accept-Language': 'zh-CN',
        'X-User-Type': 'System',
        'Request-Client-Role': 'Company'
      },
      success: (res) => {
        if (res.data) {
          const responseText = typeof res.data === 'string' ? res.data : JSON.stringify(res.data)
          parseStreamResponse(responseText)
        }
        isReply.value = false
        saveChatHistory()
        reset()
      },
      fail: (error) => {
        console.error('请求失败:', error)
        isReply.value = false

        if (chatCatch.value.length > 0 && !chatCatch.value[chatCatch.value.length - 1].msg) {
          chatCatch.value.pop()
        }

        uni.showToast({
          title: '请求失败，请重试',
          icon: 'none',
          duration: 3000
        })
      }
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    isReply.value = false
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none',
      duration: 3000
    })
  }
}

// 解析流式响应
const parseStreamResponse = (responseText: string) => {
  try {
    const lines = responseText.split('\n')

    for (const line of lines) {
      if (line.startsWith('data:')) {
        const jsonText = line.slice(5).trim()
        try {
          const json = JSON.parse(jsonText)
          const text = json.output?.text || ''
          const newSessionId = json.output?.sessionId || ''

          if (newSessionId) {
            sessionId.value = newSessionId
          }

          if (text) {
            chatResponse.value += text
            // 更新最后一条机器人消息
            if (chatCatch.value.length > 0) {
              chatCatch.value[chatCatch.value.length - 1].msg = chatResponse.value
            }
          }
        } catch (err) {
          console.error('JSON解析失败:', jsonText, err)
        }
      }
    }
  } catch (error) {
    console.error('解析响应失败:', error)
  }
}

onLoad(async (options) => {
  const token = options?.token || uni.getStorageSync('token')
  const userId = options?.userId || uni.getStorageSync('userId')

  if (token) {
    uni.setStorageSync('token', token)
  }
  if (userId) {
    uni.setStorageSync('userId', userId)
  }

  try {
    sessionId.value = uni.getStorageSync('behavior_chat_sessionId') || ''
  } catch (error) {
    console.error('加载session ID失败:', error)
  }
})

onMounted(() => {
  chatCatch.value = loadChatHistory()

  nextTick(() => {
    handleScrollToBottom()
  })
})

onShow(() => {
  // 页面显示时刷新聊天历史
  chatCatch.value = loadChatHistory()
})

onHide(() => {
  // 页面隐藏时保存聊天历史
  saveChatHistory()
})

onPullDownRefresh(() => {
  // 下拉刷新时重新加载聊天历史
  chatCatch.value = loadChatHistory()
  uni.stopPullDownRefresh()
})

// 监听器
watch(
  () => sessionId.value,
  (newVal) => {
    if (newVal) {
      try {
        uni.setStorageSync('behavior_chat_sessionId', newVal)
      } catch (error) {
        console.error('保存session ID失败:', error)
      }
    }
  },
  { deep: true }
)

watch(
  () => chatResponse.value,
  async (newVal) => {
    if (newVal && chatCatch.value.length > 0) {
      // 更新最后一条机器人消息
      chatCatch.value[chatCatch.value.length - 1].msg = newVal
      // 滚动到底部
      await handleScrollToBottom()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <view class="chat-container">
    <!-- 聊天消息区域 -->
    <scroll-view
        :scroll-y="true"
        :style="{ height: chatContainerHeight + 'px' }"
        class="chat-messages"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
    >
      <view class="message-list">
        <view
            v-for="(message, index) in chatCatch"
            :key="index"
            class="message-item"
            :class="message.role === 'user' ? 'user-message' : 'robot-message'"
        >
          <!-- 机器人消息 -->
          <view v-if="message.role === 'robot'" class="robot-msg-container">
            <view class="robot-avatar">
              <text class="robot-icon">🤖</text>
            </view>
            <view class="message-bubble robot-bubble">
              <view v-if="!message?.msg && index === chatCatch.length - 1 && isReply" class="loading-indicator">
                <text class="loading-text">AI正在思考中...</text>
                <view class="loading-dots">
                  <view class="dot"></view>
                  <view class="dot"></view>
                  <view class="dot"></view>
                </view>
              </view>
              <view v-else class="message-content">
                <text class="message-text">{{ message?.msg || '' }}</text>
                <view class="message-actions">
                  <text class="copy-btn" @tap="handleCopy(message.msg)">复制</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 用户消息 -->
          <view v-else class="user-msg-container">
            <view class="message-bubble user-bubble">
              <text class="message-text">{{ message.msg }}</text>
            </view>
            <view class="user-avatar">
              <text class="user-icon">👤</text>
            </view>
          </view>
        </view>

        <!-- 滚动锚点 -->
        <view :id="scrollViewId" class="scroll-anchor"></view>
      </view>
    </scroll-view>

    <!-- 底部工具栏 -->
    <view class="toolbar-container">
      <!-- 快捷操作按钮 -->
      <view class="quick-actions">
        <view class="action-btn" @tap="handleScrollToBottom">
          <text class="action-icon">⬇️</text>
        </view>
        <view class="action-btn" @tap="clearChatHistory">
          <text class="action-icon">🗑️</text>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="input-container">
        <view class="input-wrapper">
          <textarea
              v-model="msg"
              class="message-input"
              placeholder="问一问AI..."
              :disabled="isReply"
              :maxlength="500"
              :auto-height="true"
              :show-confirm-bar="false"
              @confirm="handleSend"
          />
          <view class="input-actions">
            <text class="char-count">{{ msg.length }}/500</text>
            <view
                class="send-btn"
                :class="{ 'send-btn-disabled': !msg.trim() || isReply, 'send-btn-loading': isReply }"
                @tap="handleSend"
            >
              <text v-if="!isReply" class="send-icon">📤</text>
              <text v-else class="stop-icon" @tap.stop="handleStop">⏹️</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.chat-messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.message-item {
  width: 100%;
}

.robot-msg-container {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.user-msg-container {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  justify-content: flex-end;
}

.robot-avatar, .user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.robot-avatar {
  background: linear-gradient(135deg, #e0f0ff 0%, #c6e0ff 100%);
  border: 2rpx solid #c6e0ff;
}

.user-avatar {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.robot-icon, .user-icon {
  font-size: 32rpx;
}

.user-icon {
  color: white;
}

.message-bubble {
  max-width: 520rpx;
  padding: 24rpx;
  border-radius: 24rpx;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.robot-bubble {
  background-color: white;
  border: 2rpx solid #e5e7eb;
  border-bottom-left-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-bubble {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  border-bottom-right-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.3);
}

.message-text {
  font-size: 28rpx;
  line-height: 1.6;
  display: block;
}

.message-actions {
  margin-top: 16rpx;
  display: flex;
  justify-content: flex-end;
}

.copy-btn {
  font-size: 24rpx;
  color: #6b7280;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: #f3f4f6;
  border: 1rpx solid #e5e7eb;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #6b7280;
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0ea5e9;
  animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.scroll-anchor {
  height: 1rpx;
}

/* 底部工具栏样式 */
.toolbar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 2rpx solid #e5e7eb;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 1000;
}

.quick-actions {
  display: flex;
  justify-content: start;
  gap: 32rpx;
  margin-bottom: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  border: 2rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  background-color: #e5e7eb;
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
}

.input-container {
  width: 100%;
}

.input-wrapper {
  background-color: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.message-input {
  width: 100%;
  min-height: 80rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  color: #374151;
}

.message-input::placeholder {
  color: #9ca3af;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.char-count {
  font-size: 24rpx;
  color: #9ca3af;
}

.send-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.3);
}

.send-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.3);
}

.send-btn-disabled {
  background: #d1d5db;
  box-shadow: none;
  opacity: 0.6;
}

.send-btn-loading {
  background: #ef4444;
  animation: pulse 2s infinite;
}

.send-icon, .stop-icon {
  font-size: 28rpx;
  color: white;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .message-bubble {
    max-width: 480rpx;
  }

  .message-text {
    font-size: 26rpx;
  }

  .robot-avatar, .user-avatar {
    width: 56rpx;
    height: 56rpx;
  }

  .robot-icon, .user-icon {
    font-size: 28rpx;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .chat-container {
    background-color: #1f2937;
  }

  .robot-bubble {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .message-actions .copy-btn {
    background-color: #4b5563;
    border-color: #6b7280;
    color: #d1d5db;
  }

  .toolbar-container {
    background-color: #1f2937;
    border-top-color: #4b5563;
  }

  .input-wrapper {
    background-color: #374151;
    border-color: #4b5563;
  }

  .message-input {
    color: #f9fafb;
  }

  .action-btn {
    background-color: #374151;
    border-color: #4b5563;
  }
}
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "行为分析小诸葛",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#f5f5f5",
    "disableScroll": true
  }
}
</route>