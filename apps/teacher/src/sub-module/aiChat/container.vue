<script setup lang="ts">
  import aiDialogBox from '@repo/components/common/aiDialogBox.vue';
  import { ref, onMounted, nextTick, watch, computed } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';

  const chatModalVisible = ref(true);
  const joinUsVisible = ref(true);
  const aiDialogRef = ref(null);
  const chatResponse = ref('');
  const sessionId = ref('');

  const token = ref(null);
  const shearUserId = ref(null);

  const headers = computed(() => {
    return {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${token.value}`,
      'LoginSource': 'PC',
      'Accept-Language': 'zh-CN',
      'X-User-Type': 'System',
      'Request-Client-Role': 'Company',
    };
  });
  const requestStreamChat = async (msg: string) => {
    chatResponse.value = '';
    const requestUrl = new URL(`${PROJECT_URLS.MAIN_PROJECT_API}/resourceRoom/behaviorAnalysis/chat/streamCall`);
    requestUrl.searchParams.append('message', msg);
    if (sessionId.value && sessionId.value !== '') requestUrl.searchParams.append('sessionId', sessionId.value);

    const response = await fetch(requestUrl.toString(), {
      method: 'PUT',
      headers: headers.value,
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');

    let partial = '';
    while (true) {
      // eslint-disable-next-line no-await-in-loop
      const { value, done } = await reader.read();
      if (done) {
        aiDialogRef.value?.saveChatHistory();
        aiDialogRef.value?.reset();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      partial += chunk;

      const lines = partial.split('\n');

      partial = lines.pop() || '';

      // eslint-disable-next-line no-restricted-syntax
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const jsonText = line.slice(5).trim();
          try {
            const json = JSON.parse(jsonText);
            const text = json.output?.text || '';
            sessionId.value = json.output?.sessionId || '';
            chatResponse.value += text;
          } catch (err) {
            console.error('⚠️ JSON 解析失败：', jsonText);
          }
        }
      }
    }
  };

  const handleChat = async (msg: string) => {
    await requestStreamChat(msg);
  };

  const form = ref({
    name: '',
    mobile: '',
  });

  const formRef = ref(null);
  const handleSubmit = async () => {
    const res = await formRef?.value.validate();
    if (res && Object.keys(res).length > 0) return;
    try {
      await request('/org/companyUser/joinUsByShear', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: form.value,
        params: {
          shearUserId: shearUserId.value,
        },
      });
      Message.success('提交成功，我们会尽快联系您！');
      joinUsVisible.value = false;
    } finally {
      /**/
    }
  };

  const handleRest = () => {
    sessionId.value = '';
    chatResponse.value = '';
  };

  onMounted(async () => {
    await nextTick();
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    token.value = urlParam.get('token');
    shearUserId.value = urlParam.get('shearUserId');
    sessionId.value = localStorage.getItem(`behavior_chat_sessionId`) || '';
  });

  watch(
    () => sessionId.value,
    (newVal) => {
      if (newVal) localStorage.setItem(`behavior_chat_sessionId`, newVal);
    },
    { deep: true },
  );
</script>

<template>
  <aiDialogBox
    ref="aiDialogRef"
    v-model:visible="chatModalVisible"
    :response="chatResponse"
    :is-p-c="false"
    :session-id="sessionId"
    @send="handleChat"
    @reset="handleRest"
  />
  <a-modal v-model:visible="joinUsVisible" width="auto" :title="false" :footer="false" :closable="false" centered>
    <div class="w-full max-w-2xl mx-auto bg-white p-8 rounded-xl">
      <h2 class="text-3xl font-bold text-center text-gray-700 mb-3">加入我们</h2>
      <p class="text-gray-500 text-center mb-8">填写以下信息，我们会尽快与您联系</p>

      <a-form ref="formRef" :model="form" layout="vertical" @submit.prevent="handleSubmit">
        <a-form-item label="姓名" field="name" :rules="[{ required: true, message: '姓名不能为空' }]">
          <a-input v-model="form.name" placeholder="请输入您的姓名" />
        </a-form-item>

        <a-form-item label="电话" field="mobile" :rules="[{ required: true, message: '手机号不能为空' }]">
          <a-input v-model="form.mobile" placeholder="请输入您的电话号码" :max-length="11" show-word-limit />
        </a-form-item>
      </a-form>

      <div class="flex justify-end mt-6 space-x-3">
        <a-button class="rounded-md px-5 py-1.5 hover:bg-gray-100" @click="joinUsVisible = false"> 取消 </a-button>
        <a-button type="primary" class="rounded-md px-5 py-1.5" @click="handleSubmit"> 提交 </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
</style>
