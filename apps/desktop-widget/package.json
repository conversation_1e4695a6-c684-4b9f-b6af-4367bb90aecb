{"name": "desktop-widget", "private": true, "version": "2.1.50", "type": "module", "scripts": {"dev": "vite", "build:deploy": "vite build --mode=deploy", "preview": "vite preview"}, "dependencies": {"@repo/components": "workspace:*", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/ui": "workspace:*", "pinia": "^2.2.2", "vue": "^3.5.13", "vue-router": "^4.2.5"}, "devDependencies": {"@arco-design/web-vue": "^2.56.1", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "typescript": "~5.6.2", "vite": "^6.0.7", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^2.2.0"}}